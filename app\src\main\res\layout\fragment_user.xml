<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="@dimen/vs_40"
    android:paddingRight="@dimen/vs_40">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/tvUserHome"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:orientation="horizontal">

            <LinearLayout
                android:id="@+id/tvHistory"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvLocalData"
                android:nextFocusRight="@+id/tvSearch"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_history" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="历史"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <!--
            <LinearLayout
                android:id="@+id/tvLive"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_live" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="直播"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>
            -->

            <LinearLayout
                android:id="@+id/tvSearch"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvHistory"
                android:nextFocusRight="@+id/tvFavorite"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_search" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="搜索"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <!--
            <LinearLayout
                android:id="@+id/tvPush"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_push" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="推送"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>
            -->

            <LinearLayout
                android:id="@+id/tvFavorite"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvSearch"
                android:nextFocusRight="@+id/tvSetting"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_collect" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="收藏"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/tvSetting"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvFavorite"
                android:nextFocusRight="@+id/tvLocalData"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">


                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_setting" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="设置"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/tvLocalData"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/vs_100"
                android:layout_gravity="center"
                android:layout_margin="@dimen/vs_5"
                android:background="@drawable/shape_user_focus"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:focusable="true"
                android:nextFocusLeft="@+id/tvSetting"
                android:nextFocusRight="@+id/tvHistory"
                android:orientation="horizontal"
                android:paddingLeft="@dimen/vs_15"
                android:paddingTop="@dimen/vs_15"
                android:paddingRight="@dimen/vs_15"
                android:paddingBottom="@dimen/vs_15">

                <ImageView
                    android:layout_width="@dimen/vs_50"
                    android:layout_height="@dimen/vs_50"
                    android:layout_gravity="center"
                    android:alpha="0.75"
                    android:src="@drawable/icon_folder" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="@dimen/vs_10"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:gravity="center"
                    android:text="本地"
                    android:textAlignment="gravity"
                    android:textColor="@color/color_CCFFFFFF"
                    android:textSize="@dimen/ts_40" />

            </LinearLayout>

        </LinearLayout>
        <com.owen.tvrecyclerview.widget.TvRecyclerView
            android:id="@+id/tvHotList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="true"
            android:clipToPadding="false"
            android:layout_below="@+id/tvUserHome"
            app:tv_horizontalSpacingWithMargins="@dimen/vs_10"
            app:tv_selectedItemIsCentered="true"
            app:tv_verticalSpacingWithMargins="@dimen/vs_10"
            android:visibility="gone" />
    </RelativeLayout>

</RelativeLayout>